import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/foundation.dart';
import 'package:railops/models/notification_tray_model.dart';
import 'package:railops/providers/notification_tray_provider.dart';
import 'package:railops/services/train_location_integration_service.dart';

/// Integration test for fallback notification system
/// Tests the complete flow from API response to notification display
void main() {
  group('Fallback Notification Integration Tests', () {
    late NotificationTrayProvider trayProvider;

    setUp(() {
      trayProvider = NotificationTrayProvider();
    });

    test('should create and display fallback notification for empty API response', () async {
      // Arrange: Mock API response with stations but no passenger data
      final mockResponse = MockEmptyResponse(
        stations: ['PNBE', 'DNR', 'GZB'],
        trainNumber: '12391',
        date: '2025-06-02',
      );

      // Act: Process the response through NotificationTrayItemFactory
      final trayItems = NotificationTrayItemFactory.fromOnboardingResponse(
        mockResponse,
        '12391',
        '2025-06-02',
      );

      // Assert: Should create exactly one fallback notification
      expect(trayItems, hasLength(1));
      
      final fallbackItem = trayItems.first;
      expect(fallbackItem.isNoActivityFallback, isTrue);
      expect(fallbackItem.stationCode, equals('PNBE')); // First station
      expect(fallbackItem.coachNumber, equals('NO_ACTIVITY'));
      expect(fallbackItem.onboardingCount, equals(0));
      expect(fallbackItem.offboardingCount, equals(0));
      expect(fallbackItem.vacantCount, equals(0));
      expect(fallbackItem.trainNumber, equals('12391'));
      expect(fallbackItem.date, equals('2025-06-02'));
      expect(fallbackItem.isRead, isFalse);

      // Act: Add to notification tray provider
      await trayProvider.addItems(trayItems);

      // Assert: Should be added to tray
      expect(trayProvider.items, hasLength(1));
      expect(trayProvider.unreadCount, equals(1));
      
      final addedItem = trayProvider.items.first;
      expect(addedItem.isNoActivityFallback, isTrue);
    });

    test('should handle mixed regular and fallback notifications correctly', () async {
      // Arrange: First add a regular notification
      final regularResponse = MockRegularResponse(
        stations: ['DDU'],
        details: {'DDU': {'A1': [1, 2, 3]}},
        trainNumber: '12391',
        date: '2025-06-02',
      );

      final regularItems = NotificationTrayItemFactory.fromOnboardingResponse(
        regularResponse,
        '12391',
        '2025-06-02',
      );

      await trayProvider.addItems(regularItems);

      // Act: Add a fallback notification
      final fallbackResponse = MockEmptyResponse(
        stations: ['PNBE'],
        trainNumber: '12391',
        date: '2025-06-02',
      );

      final fallbackItems = NotificationTrayItemFactory.fromOnboardingResponse(
        fallbackResponse,
        '12391',
        '2025-06-02',
      );

      await trayProvider.addItems(fallbackItems);

      // Assert: Should have both types of notifications
      expect(trayProvider.items, hasLength(2));
      
      final regularItem = trayProvider.items.firstWhere((item) => !item.isNoActivityFallback);
      final fallbackItem = trayProvider.items.firstWhere((item) => item.isNoActivityFallback);
      
      expect(regularItem.stationCode, equals('DDU'));
      expect(regularItem.coachNumber, equals('A1'));
      expect(regularItem.onboardingCount, equals(3));
      
      expect(fallbackItem.stationCode, equals('PNBE'));
      expect(fallbackItem.coachNumber, equals('NO_ACTIVITY'));
      expect(fallbackItem.onboardingCount, equals(0));
    });

    test('should serialize and deserialize fallback notifications correctly', () async {
      // Arrange: Create fallback notification
      final mockResponse = MockEmptyResponse(
        stations: ['PNBE'],
        trainNumber: '12391',
        date: '2025-06-02',
      );

      final trayItems = NotificationTrayItemFactory.fromOnboardingResponse(
        mockResponse,
        '12391',
        '2025-06-02',
      );

      await trayProvider.addItems(trayItems);

      // Act: Simulate app restart by creating new provider and loading from storage
      final newProvider = NotificationTrayProvider();
      await newProvider.initialize();

      // Note: In a real test, we would need to mock SharedPreferences
      // For now, we'll test the JSON serialization directly
      final originalItem = trayItems.first;
      final json = originalItem.toJson();
      final restoredItem = NotificationTrayItem.fromJson(json);

      // Assert: Should preserve fallback status
      expect(restoredItem.isNoActivityFallback, isTrue);
      expect(restoredItem.coachNumber, equals('NO_ACTIVITY'));
      expect(restoredItem.stationCode, equals(originalItem.stationCode));
    });

    test('should not create fallback when regular data exists', () async {
      // Arrange: Response with actual passenger data
      final mockResponse = MockRegularResponse(
        stations: ['PNBE', 'DNR'],
        details: {
          'PNBE': {'A1': [1, 2, 3]},
          'DNR': {'B1': [4, 5]},
        },
        trainNumber: '12391',
        date: '2025-06-02',
      );

      // Act: Process the response
      final trayItems = NotificationTrayItemFactory.fromOnboardingResponse(
        mockResponse,
        '12391',
        '2025-06-02',
      );

      // Assert: Should create regular notifications, no fallback
      expect(trayItems, hasLength(2));
      expect(trayItems.every((item) => !item.isNoActivityFallback), isTrue);
      
      final pnbeItem = trayItems.firstWhere((item) => item.stationCode == 'PNBE');
      final dnrItem = trayItems.firstWhere((item) => item.stationCode == 'DNR');
      
      expect(pnbeItem.coachNumber, equals('A1'));
      expect(pnbeItem.onboardingCount, equals(3));
      
      expect(dnrItem.coachNumber, equals('B1'));
      expect(dnrItem.onboardingCount, equals(2));
    });
  });
}

/// Mock response for testing empty passenger data scenarios
class MockEmptyResponse {
  final List<String> stations;
  final String trainNumber;
  final String date;
  final List<String>? coachNumbers;
  final Map<String, Map<String, List<int>>>? details;
  final Map<String, Map<String, List<int>>>? detailsOffBoarding;
  final Map<String, Map<String, List<int>>>? detailsVacant;

  MockEmptyResponse({
    required this.stations,
    required this.trainNumber,
    required this.date,
    this.coachNumbers,
    this.details,
    this.detailsOffBoarding,
    this.detailsVacant,
  });
}

/// Mock response for testing regular passenger data scenarios
class MockRegularResponse {
  final List<String> stations;
  final String trainNumber;
  final String date;
  final List<String>? coachNumbers;
  final Map<String, Map<String, List<int>>> details;
  final Map<String, Map<String, List<int>>>? detailsOffBoarding;
  final Map<String, Map<String, List<int>>>? detailsVacant;

  MockRegularResponse({
    required this.stations,
    required this.trainNumber,
    required this.date,
    required this.details,
    this.coachNumbers,
    this.detailsOffBoarding,
    this.detailsVacant,
  });
}
