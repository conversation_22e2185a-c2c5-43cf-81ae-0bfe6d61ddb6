import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:railops/services/train_location_integration_service.dart';
import 'package:railops/providers/notification_tray_provider.dart';
import 'package:railops/types/train_types/upcoming_station_response.dart';
import 'package:railops/models/notification_tray_model.dart';

void main() {
  group('TrainLocationIntegrationService Tests', () {
    late NotificationTrayProvider trayProvider;

    setUp(() async {
      // Mock SharedPreferences
      SharedPreferences.setMockInitialValues({});
      trayProvider = NotificationTrayProvider();
      await trayProvider.initialize();
    });

    test('should initialize local notifications without errors', () async {
      // Act
      await TrainLocationIntegrationService.initialize();

      // Assert - should not throw any exceptions
      expect(TrainLocationIntegrationService.isConfigured(), isTrue);
    });

    test('should create UpcomingStationResponse with detailsVacant property', () {
      // Arrange
      final mockApiResponse = {
        'message': 'Success',
        'stations': ['NDLS', 'SLN', 'GZB'],
        'date': '2025-01-15',
        'train_number': '12392',
        'coach_numbers': ['A1', 'B1'],
        'details': {
          'NDLS': {
            'A1': [1, 2, 3],
            'B1': [4, 5]
          }
        },
        'details_off_boarding': {
          'GZB': {
            'A1': [1, 2],
            'B1': [4]
          }
        },
        'details_vacant': {
          'NDLS': {
            'A1': [6, 7],
            'B1': [8, 9, 10]
          },
          'GZB': {
            'A1': [11],
            'B1': [12, 13]
          }
        }
      };

      // Act
      final response = UpcomingStationResponse.fromJson(mockApiResponse);

      // Assert
      expect(response.message, equals('Success'));
      expect(response.stations, equals(['NDLS', 'SLN', 'GZB']));
      expect(response.trainNumber, equals('12392'));
      expect(response.detailsVacant, isNotNull);
      expect(response.detailsVacant['NDLS'], isNotNull);
      expect(response.detailsVacant['NDLS']!['A1'], equals([6, 7]));
      expect(response.detailsVacant['GZB']!['B1'], equals([12, 13]));
    });

    test('should create notification tray items from response with vacant data', () {
      // Arrange
      final mockResponse = UpcomingStationResponse(
        message: 'Success',
        stations: ['NDLS', 'GZB'],
        date: '2025-01-15',
        trainNumber: '12392',
        coachNumbers: ['A1', 'B1'],
        details: {
          'NDLS': {
            'A1': [1, 2, 3],
            'B1': [4, 5]
          }
        },
        detailsOffBoarding: {
          'GZB': {
            'A1': [1, 2],
            'B1': [4]
          }
        },
        detailsVacant: {
          'NDLS': {
            'A1': [6, 7],
            'B1': [8, 9, 10]
          },
          'GZB': {
            'A1': [11],
            'B1': [12, 13]
          }
        },
      );

      // Act
      final trayItems = NotificationTrayItemFactory.fromOnboardingResponse(
        mockResponse,
        '12392',
        '2025-01-15',
      );

      // Assert
      expect(trayItems, isNotEmpty);
      
      // Find NDLS A1 item
      final ndlsA1Item = trayItems.firstWhere(
        (item) => item.stationCode == 'NDLS' && item.coachNumber == 'A1',
      );
      
      expect(ndlsA1Item.onboardingCount, equals(3)); // [1, 2, 3]
      expect(ndlsA1Item.offboardingCount, equals(0)); // No off-boarding at NDLS
      expect(ndlsA1Item.vacantCount, equals(2)); // [6, 7]
      
      // Find GZB B1 item
      final gzbB1Item = trayItems.firstWhere(
        (item) => item.stationCode == 'GZB' && item.coachNumber == 'B1',
      );
      
      expect(gzbB1Item.onboardingCount, equals(0)); // No boarding at GZB
      expect(gzbB1Item.offboardingCount, equals(1)); // [4]
      expect(gzbB1Item.vacantCount, equals(2)); // [12, 13]
    });

    test('should handle empty detailsVacant gracefully', () {
      // Arrange
      final mockApiResponse = {
        'message': 'Success',
        'stations': ['NDLS'],
        'date': '2025-01-15',
        'train_number': '12392',
        'coach_numbers': ['A1'],
        'details': {
          'NDLS': {
            'A1': [1, 2, 3]
          }
        },
        'details_off_boarding': {},
        'details_vacant': {} // Empty vacant data
      };

      // Act
      final response = UpcomingStationResponse.fromJson(mockApiResponse);

      // Assert
      expect(response.detailsVacant, isNotNull);
      expect(response.detailsVacant.isEmpty, isTrue);
      
      // Should still create tray items
      final trayItems = NotificationTrayItemFactory.fromOnboardingResponse(
        response,
        '12392',
        '2025-01-15',
      );
      
      expect(trayItems, isNotEmpty);
      expect(trayItems.first.vacantCount, equals(0));
    });

    test('should serialize and deserialize UpcomingStationResponse correctly', () {
      // Arrange
      final originalResponse = UpcomingStationResponse(
        message: 'Test',
        stations: ['A', 'B'],
        date: '2025-01-15',
        trainNumber: '123',
        coachNumbers: ['C1'],
        details: {'A': {'C1': [1, 2]}},
        detailsOffBoarding: {'B': {'C1': [3]}},
        detailsVacant: {'A': {'C1': [4, 5]}, 'B': {'C1': [6]}},
      );

      // Act
      final json = originalResponse.toJson();
      final deserializedResponse = UpcomingStationResponse.fromJson(json);

      // Assert
      expect(deserializedResponse.message, equals(originalResponse.message));
      expect(deserializedResponse.stations, equals(originalResponse.stations));
      expect(deserializedResponse.trainNumber, equals(originalResponse.trainNumber));
      expect(deserializedResponse.detailsVacant, equals(originalResponse.detailsVacant));
      expect(deserializedResponse.detailsVacant['A']!['C1'], equals([4, 5]));
      expect(deserializedResponse.detailsVacant['B']!['C1'], equals([6]));
    });
  });
}
