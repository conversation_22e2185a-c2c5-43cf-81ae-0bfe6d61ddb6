import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:railops/services/train_location_integration_service.dart';
import 'package:railops/providers/notification_tray_provider.dart';
import 'package:railops/types/train_types/upcoming_station_response.dart';
import 'package:railops/models/notification_tray_model.dart';

/// Test to verify that the notification functionality fixes are working correctly
/// This test simulates the exact scenario described in the user's issue
void main() {
  group('Notification Fix Verification Tests', () {
    late NotificationTrayProvider trayProvider;

    setUp(() async {
      // Mock SharedPreferences
      SharedPreferences.setMockInitialValues({});
      trayProvider = NotificationTrayProvider();
      await trayProvider.initialize();
    });

    test('should handle real API response format with detailsVacant', () async {
      print('🧪 Testing real API response format...');

      // Arrange: Simulate the exact API response format from the user's logs
      final realApiResponse = {
        "message": "Fetched successfully",
        "stations": ["NDLS", "SLN", "GZB"],
        "train_number": "12392",
        "date": "2025-01-15",
        "coach_numbers": ["A1", "B1", "S1"],
        "details": {
          "NDLS": {
            "A1": [1, 2, 3, 4, 5],
            "B1": [6, 7, 8, 9],
            "S1": [10, 11]
          },
          "SLN": {
            "A1": [12, 13],
            "S1": [14, 15, 16]
          }
        },
        "details_off_boarding": {
          "GZB": {
            "A1": [1, 2],
            "B1": [6, 7, 8],
            "S1": [10]
          }
        },
        "details_vacant": {
          "NDLS": {
            "A1": [17, 18, 19],
            "B1": [20, 21],
            "S1": [22]
          },
          "SLN": {
            "A1": [23, 24, 25, 26],
            "S1": [27, 28]
          },
          "GZB": {
            "A1": [29],
            "B1": [30, 31, 32, 33],
            "S1": [34, 35, 36]
          }
        }
      };

      // Act: Parse the response (this should not throw the detailsVacant error)
      final response = UpcomingStationResponse.fromJson(realApiResponse);

      // Assert: Verify all data is parsed correctly
      expect(response.message, equals("Fetched successfully"));
      expect(response.stations, equals(["NDLS", "SLN", "GZB"]));
      expect(response.trainNumber, equals("12392"));
      expect(response.detailsVacant, isNotNull);
      expect(response.detailsVacant.isNotEmpty, isTrue);

      // Verify specific vacant data
      expect(response.detailsVacant["NDLS"]!["A1"], equals([17, 18, 19]));
      expect(response.detailsVacant["GZB"]!["S1"], equals([34, 35, 36]));

      print('✅ Real API response parsed successfully');
    });

    test('should create notification tray items without NoSuchMethodError',
        () async {
      print('🧪 Testing notification tray item creation...');

      // Arrange: Create response with all required data
      final response = UpcomingStationResponse(
        message: "Success",
        stations: ["NDLS", "GZB"],
        trainNumber: "12392",
        date: "2025-01-15",
        coachNumbers: ["A1", "B1"],
        details: {
          "NDLS": {
            "A1": [1, 2, 3],
            "B1": [4, 5]
          }
        },
        detailsOffBoarding: {
          "GZB": {
            "A1": [1, 2],
            "B1": [4]
          }
        },
        detailsVacant: {
          "NDLS": {
            "A1": [6, 7],
            "B1": [8, 9, 10]
          },
          "GZB": {
            "A1": [11],
            "B1": [12, 13]
          }
        },
      );

      // Act: This should not throw NoSuchMethodError for detailsVacant
      final trayItems = NotificationTrayItemFactory.fromOnboardingResponse(
        response,
        "12392",
        "2025-01-15",
      );

      // Assert: Verify items are created with correct counts
      expect(trayItems, isNotEmpty);
      expect(trayItems.length, equals(4)); // 2 stations × 2 coaches = 4 items

      // Verify specific item data
      final ndlsA1 = trayItems.firstWhere(
          (item) => item.stationCode == "NDLS" && item.coachNumber == "A1");
      expect(ndlsA1.onboardingCount, equals(3)); // [1, 2, 3]
      expect(ndlsA1.offboardingCount, equals(0)); // No off-boarding at NDLS
      expect(ndlsA1.vacantCount, equals(2)); // [6, 7]

      final gzbB1 = trayItems.firstWhere(
          (item) => item.stationCode == "GZB" && item.coachNumber == "B1");
      expect(gzbB1.onboardingCount, equals(0)); // No boarding at GZB
      expect(gzbB1.offboardingCount, equals(1)); // [4]
      expect(gzbB1.vacantCount, equals(2)); // [12, 13]

      print('✅ Notification tray items created successfully');
      print('   - Total items: ${trayItems.length}');
      print(
          '   - NDLS A1: ${ndlsA1.onboardingCount} boarding, ${ndlsA1.vacantCount} vacant');
      print(
          '   - GZB B1: ${gzbB1.offboardingCount} off-boarding, ${gzbB1.vacantCount} vacant');
    });

    test('should add items to notification tray successfully', () async {
      print('🧪 Testing notification tray integration...');

      // Arrange: Create test items
      final testItems = [
        NotificationTrayItem(
          id: 'test_1',
          stationCode: 'NDLS',
          coachNumber: 'A1',
          onboardingCount: 5,
          offboardingCount: 0,
          vacantCount: 3,
          timestamp: DateTime.now(),
          trainNumber: '12392',
          date: '2025-01-15',
        ),
        NotificationTrayItem(
          id: 'test_2',
          stationCode: 'GZB',
          coachNumber: 'B1',
          onboardingCount: 0,
          offboardingCount: 2,
          vacantCount: 4,
          timestamp: DateTime.now(),
          trainNumber: '12392',
          date: '2025-01-15',
        ),
      ];

      // Act: Add items to tray
      await trayProvider.addItems(testItems);

      // Assert: Verify items are in tray
      final allItems = trayProvider.items;
      expect(allItems.length, equals(2));

      final stationGroups = trayProvider.itemsByStation;
      expect(stationGroups.length, equals(2)); // NDLS and GZB
      expect(stationGroups['NDLS']?.items.length, equals(1));
      expect(stationGroups['GZB']?.items.length, equals(1));

      print('✅ Notification tray integration successful');
      print('   - Items added: ${allItems.length}');
      print('   - Station groups: ${stationGroups.keys.join(", ")}');
    });

    test('should handle notification initialization gracefully', () async {
      print('🧪 Testing notification initialization...');

      // Act: Initialize notifications (should handle Flutter binding issues gracefully)
      await TrainLocationIntegrationService.initialize();

      // Assert: Should not throw exceptions
      final configStatus =
          TrainLocationIntegrationService.getConfigurationStatus();
      expect(configStatus, isNotNull);
      expect(configStatus.containsKey('firebase_initialized'), isTrue);
      expect(configStatus.containsKey('notification_permissions'), isTrue);

      print('✅ Notification initialization handled gracefully');
      print('   - Configuration status: ${configStatus.keys.join(", ")}');
    });

    test('should demonstrate complete notification flow', () async {
      print('🧪 Testing complete notification flow...');

      // Arrange: Real-world scenario data
      final apiResponseData = {
        "message": "Fetched successfully",
        "stations": ["NDLS", "SLN", "GZB"],
        "train_number": "12392",
        "date": "2025-01-15",
        "coach_numbers": ["A1", "B1"],
        "details": {
          "NDLS": {
            "A1": [1, 2, 3],
            "B1": [4, 5]
          },
          "SLN": {
            "A1": [6, 7],
            "B1": [8]
          }
        },
        "details_off_boarding": {
          "GZB": {
            "A1": [1, 2],
            "B1": [4, 5]
          }
        },
        "details_vacant": {
          "NDLS": {
            "A1": [9, 10],
            "B1": [11, 12, 13]
          },
          "SLN": {
            "A1": [14],
            "B1": [15, 16]
          },
          "GZB": {
            "A1": [17, 18, 19],
            "B1": [20]
          }
        }
      };

      // Act: Complete flow
      // 1. Parse API response
      final response = UpcomingStationResponse.fromJson(apiResponseData);

      // 2. Create notification tray items
      final trayItems = NotificationTrayItemFactory.fromOnboardingResponse(
        response,
        response.trainNumber,
        response.date,
      );

      // 3. Add to notification tray
      await trayProvider.addItems(trayItems);

      // Assert: Verify complete flow
      expect(response.detailsVacant, isNotNull);
      expect(trayItems, isNotEmpty);
      expect(trayProvider.items.length, equals(trayItems.length));

      // Verify notification tray table format
      final stationGroups = trayProvider.itemsByStation;
      for (final entry in stationGroups.entries) {
        final stationCode = entry.key;
        final group = entry.value;

        print('   Station: $stationCode');
        for (final item in group.items) {
          print(
              '     Coach ${item.coachNumber}: ${item.onboardingCount} boarding, ${item.offboardingCount} off-boarding, ${item.vacantCount} vacant');
        }
      }

      print('✅ Complete notification flow successful');
      print('   - API response parsed: ✓');
      print('   - Tray items created: ${trayItems.length}');
      print('   - Items added to tray: ✓');
      print('   - Station groups: ${stationGroups.length}');
    });
  });
}
