// Models for the notification tray system
// Handles station-coach-count data structure for train notifications

import 'package:flutter/foundation.dart';

enum NotificationTrayItemType {
  onboarding, // Green - passengers boarding
  offboarding, // Orange - passengers deboarding
  vacant, // Grey - vacant seats
}

/// Individual notification tray item representing a station-coach combination
class NotificationTrayItem {
  final String id;
  final String stationCode;
  final String coachNumber;
  final int onboardingCount;
  final int offboardingCount;
  final int vacantCount;
  final DateTime timestamp;
  final bool isRead;
  final String trainNumber;
  final String date;
  final bool isNoActivityFallback;

  NotificationTrayItem({
    required this.id,
    required this.stationCode,
    required this.coachNumber,
    required this.onboardingCount,
    required this.offboardingCount,
    required this.vacantCount,
    required this.timestamp,
    required this.trainNumber,
    required this.date,
    this.isRead = false,
    this.isNoActivityFallback = false,
  });

  /// Get total passenger count (onboarding + offboarding)
  int get totalPassengerCount => onboardingCount + offboardingCount;

  /// Check if this item has any passenger activity
  bool get hasActivity => totalPassengerCount > 0 || vacantCount > 0;

  /// Get the primary notification type based on highest count
  NotificationTrayItemType get primaryType {
    if (onboardingCount >= offboardingCount && onboardingCount >= vacantCount) {
      return NotificationTrayItemType.onboarding;
    } else if (offboardingCount >= vacantCount) {
      return NotificationTrayItemType.offboarding;
    } else {
      return NotificationTrayItemType.vacant;
    }
  }

  /// Create a copy with updated read status
  NotificationTrayItem copyWith({
    String? id,
    String? stationCode,
    String? coachNumber,
    int? onboardingCount,
    int? offboardingCount,
    int? vacantCount,
    DateTime? timestamp,
    bool? isRead,
    String? trainNumber,
    String? date,
    bool? isNoActivityFallback,
  }) {
    return NotificationTrayItem(
      id: id ?? this.id,
      stationCode: stationCode ?? this.stationCode,
      coachNumber: coachNumber ?? this.coachNumber,
      onboardingCount: onboardingCount ?? this.onboardingCount,
      offboardingCount: offboardingCount ?? this.offboardingCount,
      vacantCount: vacantCount ?? this.vacantCount,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      trainNumber: trainNumber ?? this.trainNumber,
      date: date ?? this.date,
      isNoActivityFallback: isNoActivityFallback ?? this.isNoActivityFallback,
    );
  }

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'station_code': stationCode,
      'coach_number': coachNumber,
      'onboarding_count': onboardingCount,
      'offboarding_count': offboardingCount,
      'vacant_count': vacantCount,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'is_read': isRead,
      'train_number': trainNumber,
      'date': date,
      'is_no_activity_fallback': isNoActivityFallback,
    };
  }

  /// Create from JSON
  factory NotificationTrayItem.fromJson(Map<String, dynamic> json) {
    return NotificationTrayItem(
      id: json['id'],
      stationCode: json['station_code'],
      coachNumber: json['coach_number'],
      onboardingCount: json['onboarding_count'] ?? 0,
      offboardingCount: json['offboarding_count'] ?? 0,
      vacantCount: json['vacant_count'] ?? 0,
      timestamp: DateTime.fromMillisecondsSinceEpoch(json['timestamp']),
      isRead: json['is_read'] ?? false,
      trainNumber: json['train_number'],
      date: json['date'],
      isNoActivityFallback: json['is_no_activity_fallback'] ?? false,
    );
  }

  @override
  String toString() {
    return 'NotificationTrayItem(station: $stationCode, coach: $coachNumber, '
        'onboarding: $onboardingCount, offboarding: $offboardingCount, '
        'vacant: $vacantCount, read: $isRead, fallback: $isNoActivityFallback)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationTrayItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Grouped notification data for a specific station
class StationNotificationGroup {
  final String stationCode;
  final List<NotificationTrayItem> items;
  final DateTime latestTimestamp;

  StationNotificationGroup({
    required this.stationCode,
    required this.items,
    required this.latestTimestamp,
  });

  /// Get total counts across all coaches for this station
  int get totalOnboardingCount =>
      items.fold(0, (sum, item) => sum + item.onboardingCount);

  int get totalOffboardingCount =>
      items.fold(0, (sum, item) => sum + item.offboardingCount);

  int get totalVacantCount =>
      items.fold(0, (sum, item) => sum + item.vacantCount);

  /// Get unread count for this station
  int get unreadCount => items.where((item) => !item.isRead).length;

  /// Check if any items in this group are unread
  bool get hasUnreadItems => unreadCount > 0;

  /// Get coaches handled by this CA (from the items)
  List<String> get coachNumbers =>
      items.map((item) => item.coachNumber).toSet().toList()..sort();

  /// Mark all items in this group as read
  List<NotificationTrayItem> markAllAsRead() {
    return items.map((item) => item.copyWith(isRead: true)).toList();
  }
}

/// Summary data for notification tray display
class NotificationTraySummary {
  final int totalUnreadCount;
  final int totalOnboardingCount;
  final int totalOffboardingCount;
  final int totalVacantCount;
  final List<String> activeStations;
  final List<String> activeCoaches;
  final DateTime? lastUpdateTime;

  NotificationTraySummary({
    required this.totalUnreadCount,
    required this.totalOnboardingCount,
    required this.totalOffboardingCount,
    required this.totalVacantCount,
    required this.activeStations,
    required this.activeCoaches,
    this.lastUpdateTime,
  });

  /// Check if there are any active notifications
  bool get hasActiveNotifications =>
      totalOnboardingCount > 0 ||
      totalOffboardingCount > 0 ||
      totalVacantCount > 0;

  /// Get total passenger activity count
  int get totalPassengerActivity =>
      totalOnboardingCount + totalOffboardingCount;

  /// Create empty summary
  factory NotificationTraySummary.empty() {
    return NotificationTraySummary(
      totalUnreadCount: 0,
      totalOnboardingCount: 0,
      totalOffboardingCount: 0,
      totalVacantCount: 0,
      activeStations: [],
      activeCoaches: [],
      lastUpdateTime: null,
    );
  }

  @override
  String toString() {
    return 'NotificationTraySummary(unread: $totalUnreadCount, '
        'onboarding: $totalOnboardingCount, offboarding: $totalOffboardingCount, '
        'vacant: $totalVacantCount, stations: ${activeStations.length})';
  }
}

/// Factory class to create notification tray items from API responses
class NotificationTrayItemFactory {
  /// Create notification tray items from OnboardingResponse
  static List<NotificationTrayItem> fromOnboardingResponse(
    dynamic response, // OnboardingResponse type
    String trainNumber,
    String date,
  ) {
    final List<NotificationTrayItem> items = [];
    final timestamp = DateTime.now();

    if (kDebugMode) {
      print(
          '🏭 NotificationTrayItemFactory: Processing response for train $trainNumber');
      print('📊 Stations: ${response.stations?.length ?? 0}');
      print('🚂 Coach numbers: ${response.coachNumbers?.length ?? 0}');
    }

    // Process each station
    if (response.stations != null) {
      for (final stationCode in response.stations!) {
        // Get all coaches for this station
        final Set<String> allCoaches = <String>{};

        // Collect coaches from all data sources
        if (response.details != null &&
            response.details!.containsKey(stationCode)) {
          allCoaches.addAll(response.details![stationCode]!.keys);
        }
        if (response.detailsOffBoarding != null &&
            response.detailsOffBoarding!.containsKey(stationCode)) {
          allCoaches.addAll(response.detailsOffBoarding![stationCode]!.keys);
        }
        if (response.detailsVacant != null &&
            response.detailsVacant!.containsKey(stationCode)) {
          allCoaches.addAll(response.detailsVacant![stationCode]!.keys);
        }

        // Create items for each coach
        for (final coachNumber in allCoaches) {
          final onboardingCount =
              _getCountForCoach(response.details, stationCode, coachNumber);
          final offboardingCount = _getCountForCoach(
              response.detailsOffBoarding, stationCode, coachNumber);
          final vacantCount = _getCountForCoach(
              response.detailsVacant, stationCode, coachNumber);

          if (kDebugMode) {
            print('🔍 Station: $stationCode, Coach: $coachNumber');
            print('   📈 Onboarding: $onboardingCount');
            print('   📉 Offboarding: $offboardingCount');
            print('   🪑 Vacant: $vacantCount');
          }

          // Only create item if there's any activity
          if (onboardingCount > 0 || offboardingCount > 0 || vacantCount > 0) {
            final item = NotificationTrayItem(
              id: '${trainNumber}_${stationCode}_${coachNumber}_${timestamp.millisecondsSinceEpoch}',
              stationCode: stationCode,
              coachNumber: coachNumber,
              onboardingCount: onboardingCount,
              offboardingCount: offboardingCount,
              vacantCount: vacantCount,
              timestamp: timestamp,
              trainNumber: trainNumber,
              date: date,
              isRead: false,
            );
            items.add(item);

            if (kDebugMode) {
              print(
                  '   ✅ Created notification tray item for $stationCode-$coachNumber');
            }
          } else {
            if (kDebugMode) {
              print('   ⏭️ Skipped $stationCode-$coachNumber (no activity)');
            }
          }
        }
      }
    }

    if (kDebugMode) {
      print(
          '🏁 NotificationTrayItemFactory: Created ${items.length} notification tray items');
      if (items.isEmpty) {
        print(
            '⚠️ No notification tray items created - all passenger arrays were empty');
      }
    }

    // If no items were created but we have stations, create fallback notification
    if (items.isEmpty &&
        response.stations != null &&
        response.stations!.isNotEmpty) {
      final fallbackItem = _createFallbackNotification(
        response.stations!,
        trainNumber,
        date,
        timestamp,
      );
      if (fallbackItem != null) {
        items.add(fallbackItem);

        if (kDebugMode) {
          print(
              '📭 Created fallback notification for stations with no passenger activity: ${response.stations!.join(', ')}');
        }
      }
    }

    return items;
  }

  /// Helper method to get count for a specific coach from nested map structure
  static int _getCountForCoach(
    Map<String, Map<String, List<int>>>? data,
    String stationCode,
    String coachNumber,
  ) {
    if (data == null || !data.containsKey(stationCode)) {
      return 0;
    }

    final stationData = data[stationCode]!;
    if (!stationData.containsKey(coachNumber)) {
      return 0;
    }

    return stationData[coachNumber]!.length;
  }

  /// Create a fallback notification when no passenger activity is detected
  static NotificationTrayItem? _createFallbackNotification(
    List<String> stations,
    String trainNumber,
    String date,
    DateTime timestamp,
  ) {
    if (stations.isEmpty) return null;

    // Use the first station as the primary station for the fallback notification
    final primaryStation = stations.first;

    return NotificationTrayItem(
      id: '${trainNumber}_${primaryStation}_FALLBACK_${timestamp.millisecondsSinceEpoch}',
      stationCode: primaryStation,
      coachNumber: 'NO_ACTIVITY', // Special coach indicator for fallback
      onboardingCount: 0,
      offboardingCount: 0,
      vacantCount: 0,
      timestamp: timestamp,
      trainNumber: trainNumber,
      date: date,
      isRead: false,
      isNoActivityFallback: true,
    );
  }
}
