class UpcomingStationResponse {
  final String message;
  final List<String> stations;
  final String date;
  final String trainNumber;
  final List<String> coachNumbers;
  final Map<String, Map<String, List<int>>> details;
  final Map<String, Map<String, List<int>>> detailsOffBoarding;
  final Map<String, Map<String, List<int>>> detailsVacant;

  UpcomingStationResponse({
    required this.message,
    required this.stations,
    required this.date,
    required this.trainNumber,
    required this.coachNumbers,
    required this.details,
    required this.detailsOffBoarding,
    required this.detailsVacant,
  });

  factory UpcomingStationResponse.fromJson(Map<String, dynamic> json) {
    return UpcomingStationResponse(
      message: json['message'] ?? '',
      stations: List<String>.from(json['stations'] ?? []),
      date: json['date'] ?? '',
      trainNumber: json['train_number'] ?? '',
      coachNumbers: List<String>.from(json['coach_numbers'] ?? []),
      details: _parseDetails(json['details']),
      detailsOffBoarding: _parseDetails(json['details_off_boarding']),
      detailsVacant: _parseDetails(json['details_vacant']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'message': message,
      'stations': stations,
      'date': date,
      'train_number': trainNumber,
      'coach_numbers': coachNumbers,
      'details': details,
      'details_off_boarding': detailsOffBoarding,
      'details_vacant': detailsVacant,
    };
  }

  static Map<String, Map<String, List<int>>> _parseDetails(dynamic json) {
    final Map<String, Map<String, List<int>>> details = {};
    if (json != null && json is Map) {
      json.forEach((station, coaches) {
        details[station.toString()] = {};
        if (coaches is Map) {
          coaches.forEach((coach, berthNumbers) {
            if (berthNumbers is List) {
              details[station.toString()]![coach.toString()] =
                  List<int>.from(berthNumbers);
            }
          });
        }
      });
    }
    return details;
  }
}
