import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/index.dart';
import 'package:railops/models/notification_provider.dart';
import 'package:railops/providers/notification_tray_provider.dart';
import 'package:railops/route_logger.dart';
import 'package:railops/routes.dart';
import 'package:railops/screens/add_user/add_new_user.dart';
import 'package:railops/screens/assign_ehk_ca_screen/assign_ehk_ca_screen.dart';
import 'package:railops/screens/assign_obhs/assign_obhs_screen.dart';
import 'package:railops/screens/attendance/attendance_screen.dart';
import 'package:railops/screens/attendance/upload_manager.dart';
import 'package:railops/screens/customer_care/customer_care_screen.dart';
import 'package:railops/screens/edit_train/edit_train_screen.dart';
import 'package:railops/screens/enable_disable_user/enable_disable_user.dart';
import 'package:railops/screens/feedback_screens/passenger_feedback_screen.dart';
import 'package:railops/screens/index.dart';
import 'package:railops/screens/notification_center/notification_center_screen.dart';
import 'package:railops/screens/notification_settings/notification_settings_screen.dart';
import 'package:railops/screens/notification_testing/notification_testing_screen.dart';
import 'package:railops/screens/pdf_screen/pdf_screen.dart';
import 'package:railops/screens/pnr_screen/pnr_status.dart';
import 'package:railops/screens/profile_screen/add_train_screen.dart';
import 'package:railops/screens/profile_screen/change_whatsapp_screen.dart';
import 'package:railops/screens/profile_screen/change_email_screen.dart';
import 'package:railops/screens/profile_screen/change_mobile_screen.dart';
import 'package:railops/screens/profile_screen/change_password_screen.dart';
import 'package:railops/screens/profile_screen/edit_profile_screen.dart';
import 'package:railops/screens/rail_sathi_qr/rail_sathi_qr_screen.dart';
import 'package:railops/screens/rail_sathi/rail_sathi_screen.dart';
import 'package:railops/screens/request_user_management/requested_user_screen.dart';
import 'package:railops/screens/train_details/train_details_screen.dart';
import 'package:railops/screens/trip_report/widget/IssueScreen.dart';
import 'package:railops/screens/update_user/update_user_screen.dart';
import 'package:railops/screens/trip_report/trip_report_screen.dart';
import 'package:railops/screens/upload_screen/upload_screen.dart';
import 'package:railops/screens/user_screen/forgot_password_screen.dart';
import 'package:railops/screens/user_screen/login_mobile_screen.dart';
import 'package:railops/screens/user_screen/mobile_otp_screen.dart';
import 'package:railops/screens/map_screen/map_screen.dart';
import 'package:railops/services/firebase_messaging_service.dart';
import 'package:railops/utils/fetch_location.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_core/firebase_core.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  await Firebase.initializeApp();

  // Initialize Firebase Messaging Service
  await FirebaseMessagingService().initialize();

  SharedPreferences prefs = await SharedPreferences.getInstance();
  String? lastRoute = prefs.getString('lastRoute') ?? Routes.splashScreen;

  runApp(MainApp(initialRoute: lastRoute));

  if (!kIsWeb) {
    await LocationTracker.initialize();
    bool isAuthenticated = prefs.getString('authToken') != null;
    if (isAuthenticated) {
      await LocationTracker.startTracking();
    }
  }
}

class MainApp extends StatelessWidget {
  final String initialRoute;

  const MainApp({super.key, required this.initialRoute});

  @override
  Widget build(BuildContext context) {
    RouteLogger routes = RouteLogger();

    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthModel()),
        ChangeNotifierProvider(create: (_) => UserModel()),
        ChangeNotifierProvider(create: (_) => UploadManager()),
        ChangeNotifierProvider(create: (_) => NotificationProvider()),
        ChangeNotifierProvider(create: (_) => NotificationTrayProvider()),
      ],
      child: MaterialApp(
        title: 'RailOps',
        navigatorKey:
            navigatorKey, // Add global navigator key for FCM navigation
        theme: ThemeData(
          primarySwatch: Colors.blue,
          appBarTheme: const AppBarTheme(
            centerTitle: true, // Center-align the AppBar title
            titleTextStyle: TextStyle(
              fontSize: 20.0,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
        navigatorObservers: [routes],
        initialRoute: Routes.splashScreen,
        routes: {
          Routes.login: (context) => const LoginScreen(),
          Routes.home: (context) => const HomeScreen(),
          Routes.splashScreen: (context) => const SplashScreen(),
          Routes.signUp: (context) => const SignUpScreen(),
          Routes.requestUser: (context) => const RequestUserScreen(),
          Routes.mobileLogin: (context) => const LoginMobileScreen(),
          Routes.otpEnter: (context) => const MobileOtpScreen(),
          Routes.forgotPassword: (context) => const ForgotPasswordScreen(),
          Routes.TrainDetails: (context) => TrainDetailsScreen(),
          Routes.editProfle: (context) => const EditProfileScreen(),
          Routes.changeEmail: (context) => const ChangeEmailScreen(),
          Routes.changePassword: (context) => const ChangePasswordScreen(),
          Routes.changeMobile: (context) => const ChangeMobileScreen(),
          Routes.changeWhatsapp: (context) => const ChangeWhatsappScreen(),
          Routes.addTrainProfile: (context) => const AddTrainScreen(),
          Routes.mapScreen: (context) => const MapScreen(),
          Routes.uploadData: (context) => const UploadJsonScreen(),
          Routes.AssignEhkCa: (context) => const AssignEhkCaScreen(),
          Routes.editTrain: (context) => const EditTrainScreen(),
          Routes.attendance: (context) => AttendanceScreen(),
          Routes.addTrain: (context) => const AddTrainScreen(),
          Routes.passengerFeedbackScreen: (context) =>
              const StationPassengerFeedbackScreen(),
          Routes.assignObhs: (context) => const AssignObhsScreen(),
          Routes.pdfScreen: (context) => const PdfScreen(),
          Routes.userInfo: (context) => const EnableDisableUser(),
          Routes.addUser: (context) => const AddUserScreen(),
          Routes.tripReport: (context) => const TripReportScreen(),
          Routes.updateUser: (context) => const UpdateUserScreen(),
          Routes.issueScrren: (context) => IssueScreen(),
          Routes.pnrScrren: (context) => const PnrStatus(),
          Routes.railSathiQr: (context) => const RailSathiQrScreen(),
          Routes.customerCare: (context) => CustomerCareScreen(),
          Routes.railSathi: (context) => const RailSathi(),
          Routes.notificationCenter: (context) =>
              const NotificationCenterScreen(),
          Routes.notificationSettings: (context) =>
              const NotificationSettingsScreen(),
          Routes.notificationTesting: (context) =>
              const NotificationTestingScreen(),
        },
      ),
    );
  }
}
