import 'dart:async';
import 'dart:isolate';
import 'package:flutter/foundation.dart';
import 'package:railops/services/notification_services/notification_integration_helper.dart';
import 'package:railops/models/notification_tray_model.dart';

/// Service to handle notification operations with performance optimizations
/// Prevents main thread blocking and provides caching/throttling
class NotificationPerformanceService {
  static final NotificationPerformanceService _instance = NotificationPerformanceService._internal();
  factory NotificationPerformanceService() => _instance;
  NotificationPerformanceService._internal();

  // Performance tracking
  final Map<String, DateTime> _lastApiCalls = {};
  final Map<String, dynamic> _apiCache = {};
  static const Duration _apiCooldown = Duration(seconds: 2);
  static const Duration _cacheExpiry = Duration(minutes: 5);

  /// Check if API call should be throttled
  bool shouldThrottleApiCall(String endpoint) {
    final lastCall = _lastApiCalls[endpoint];
    if (lastCall == null) return false;
    return DateTime.now().difference(lastCall) < _apiCooldown;
  }

  /// Get cached API response if available and not expired
  dynamic getCachedResponse(String cacheKey) {
    final cached = _apiCache[cacheKey];
    if (cached == null) return null;
    
    final timestamp = cached['timestamp'] as DateTime;
    if (DateTime.now().difference(timestamp) > _cacheExpiry) {
      _apiCache.remove(cacheKey);
      return null;
    }
    
    return cached['response'];
  }

  /// Cache API response with timestamp
  void cacheResponse(String cacheKey, dynamic response) {
    _apiCache[cacheKey] = {
      'response': response,
      'timestamp': DateTime.now(),
    };
    
    // Clean old cache entries
    _cleanOldCacheEntries();
  }

  /// Clean expired cache entries
  void _cleanOldCacheEntries() {
    final now = DateTime.now();
    _apiCache.removeWhere((key, value) {
      final timestamp = value['timestamp'] as DateTime;
      return now.difference(timestamp) > _cacheExpiry;
    });
  }

  /// Execute API call with performance optimizations
  Future<T> executeOptimizedApiCall<T>(
    String endpoint,
    String cacheKey,
    Future<T> Function() apiCall,
  ) async {
    // Check throttling
    if (shouldThrottleApiCall(endpoint)) {
      throw Exception(
        'API calls to $endpoint are being throttled. Please wait ${_apiCooldown.inSeconds} seconds between calls.'
      );
    }

    // Check cache
    final cached = getCachedResponse(cacheKey);
    if (cached != null) {
      if (kDebugMode) {
        print('🚀 Using cached response for: $cacheKey');
      }
      return cached as T;
    }

    // Execute API call
    _lastApiCalls[endpoint] = DateTime.now();
    
    try {
      final response = await apiCall();
      
      // Cache successful response
      cacheResponse(cacheKey, response);
      
      if (kDebugMode) {
        print('✅ API call completed and cached: $endpoint');
      }
      
      return response;
    } catch (e) {
      if (kDebugMode) {
        print('❌ API call failed: $endpoint - $e');
      }
      rethrow;
    }
  }

  /// Execute notification processing in background isolate
  static Future<List<NotificationTrayItem>> processNotificationsInBackground({
    required String lat,
    required String lng,
    required String token,
    required String trainNumber,
    required String date,
  }) async {
    try {
      // Create isolate for background processing
      final receivePort = ReceivePort();
      
      final isolate = await Isolate.spawn(
        _backgroundNotificationProcessor,
        {
          'sendPort': receivePort.sendPort,
          'lat': lat,
          'lng': lng,
          'token': token,
          'trainNumber': trainNumber,
          'date': date,
        },
      );

      // Wait for result from isolate
      final result = await receivePort.first;
      
      // Clean up isolate
      isolate.kill(priority: Isolate.immediate);
      
      if (result is Map && result.containsKey('error')) {
        throw Exception(result['error']);
      }
      
      // Convert result back to NotificationTrayItem list
      final itemsData = result as List<Map<String, dynamic>>;
      return itemsData.map((data) => NotificationTrayItem.fromJson(data)).toList();
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Background notification processing failed: $e');
      }
      rethrow;
    }
  }

  /// Background isolate function for notification processing
  static void _backgroundNotificationProcessor(Map<String, dynamic> params) async {
    final sendPort = params['sendPort'] as SendPort;
    
    try {
      // This would normally call the API and process notifications
      // For now, we'll simulate the processing
      await Future.delayed(const Duration(milliseconds: 500));
      
      // Create sample notification tray items
      final timestamp = DateTime.now();
      final items = [
        {
          'id': 'bg_${timestamp.millisecondsSinceEpoch}_1',
          'stationCode': 'NDLS',
          'coachNumber': 'A1',
          'onboardingCount': 5,
          'offboardingCount': 3,
          'vacantCount': 2,
          'timestamp': timestamp.toIso8601String(),
          'trainNumber': params['trainNumber'],
          'date': params['date'],
          'isRead': false,
        },
        {
          'id': 'bg_${timestamp.millisecondsSinceEpoch}_2',
          'stationCode': 'NDLS',
          'coachNumber': 'B3',
          'onboardingCount': 7,
          'offboardingCount': 4,
          'vacantCount': 1,
          'timestamp': timestamp.toIso8601String(),
          'trainNumber': params['trainNumber'],
          'date': params['date'],
          'isRead': false,
        },
      ];
      
      sendPort.send(items);
    } catch (e) {
      sendPort.send({'error': e.toString()});
    }
  }

  /// Debounced API call execution
  Timer? _debounceTimer;
  
  void executeDebounced(
    Duration delay,
    VoidCallback callback,
  ) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(delay, callback);
  }

  /// Clean up resources
  void dispose() {
    _debounceTimer?.cancel();
    _apiCache.clear();
    _lastApiCalls.clear();
  }

  /// Get performance statistics
  Map<String, dynamic> getPerformanceStats() {
    return {
      'cached_responses': _apiCache.length,
      'tracked_endpoints': _lastApiCalls.length,
      'cache_expiry_minutes': _cacheExpiry.inMinutes,
      'api_cooldown_seconds': _apiCooldown.inSeconds,
    };
  }
}
