import 'package:flutter/foundation.dart';
import 'package:railops/models/notification_tray_model.dart';
import 'package:railops/services/train_services/upcoming_station_service.dart';

/// Test script to verify LKO coordinates return better passenger data than NDLS
void main() async {
  print('🧪 Testing LKO vs NDLS coordinates for passenger data...\n');

  // Test coordinates
  const testToken = 'test-user-token'; // Replace with actual user token

  final testLocations = {
    'Lucknow (LKO)': {'lat': '26.8467', 'lng': '80.9462'},
    'New Delhi (NDLS)': {'lat': '28.6139', 'lng': '77.2090'},
    'Mumbai Central (BCT)': {'lat': '19.0330', 'lng': '72.8397'},
    'Kanpur Central (CNB)': {'lat': '26.4499', 'lng': '80.3319'},
  };

  for (final location in testLocations.entries) {
    await testLocation(
      location.key,
      location.value['lat']!,
      location.value['lng']!,
      testToken,
    );
    print(''); // Add spacing between tests
  }
}

Future<void> testLocation(
  String locationName,
  String lat,
  String lng,
  String token,
) async {
  print('📍 Testing $locationName ($lat, $lng)');
  print('=' * 50);

  try {
    // Call the API
    final response = await UpcomingStationService.fetchUpcomingStationDetails(
      lat: lat,
      lng: lng,
      token: token,
    );

    print('✅ API call successful');
    print('📊 Response data:');
    print('   - Train: ${response.trainNumber}');
    print('   - Stations: ${response.stations?.length ?? 0}');
    print('   - Coach numbers: ${response.coachNumbers?.length ?? 0}');

    // Analyze passenger data
    int totalOnboarding = 0;
    int totalOffboarding = 0;
    int totalVacant = 0;
    int stationsWithData = 0;
    int coachesWithData = 0;

    if (response.stations != null) {
      for (final station in response.stations!) {
        bool stationHasData = false;

        // Check onboarding data
        if (response.details?.containsKey(station) == true) {
          final stationData = response.details![station]!;
          for (final coach in stationData.keys) {
            final count = stationData[coach]!.length;
            if (count > 0) {
              totalOnboarding += count;
              stationHasData = true;
              coachesWithData++;
            }
          }
        }

        // Check offboarding data
        if (response.detailsOffBoarding?.containsKey(station) == true) {
          final stationData = response.detailsOffBoarding![station]!;
          for (final coach in stationData.keys) {
            final count = stationData[coach]!.length;
            if (count > 0) {
              totalOffboarding += count;
              stationHasData = true;
            }
          }
        }

        // Check vacant data
        if (response.detailsVacant?.containsKey(station) == true) {
          final stationData = response.detailsVacant![station]!;
          for (final coach in stationData.keys) {
            final count = stationData[coach]!.length;
            if (count > 0) {
              totalVacant += count;
              stationHasData = true;
            }
          }
        }

        if (stationHasData) {
          stationsWithData++;
        }
      }
    }

    print('📈 Passenger data analysis:');
    print('   - Total onboarding: $totalOnboarding');
    print('   - Total offboarding: $totalOffboarding');
    print('   - Total vacant: $totalVacant');
    print('   - Stations with data: $stationsWithData');
    print('   - Coaches with data: $coachesWithData');

    // Test notification tray item creation
    final trayItems = NotificationTrayItemFactory.fromOnboardingResponse(
      response,
      response.trainNumber ?? 'TEST123',
      '2025-01-15',
    );

    print('🔔 Notification tray items: ${trayItems.length}');

    if (trayItems.isNotEmpty) {
      print(
          '✅ SUCCESS: This location has passenger data and creates notifications!');
      for (final item in trayItems.take(3)) {
        print('   - ${item.stationCode}-${item.coachNumber}: '
            'On:${item.onboardingCount}, Off:${item.offboardingCount}, Vacant:${item.vacantCount}');
      }
      if (trayItems.length > 3) {
        print('   ... and ${trayItems.length - 3} more items');
      }
    } else {
      print('⚠️ No notification tray items created - empty passenger data');
    }
  } catch (e) {
    print('❌ API call failed: $e');
  }
}
